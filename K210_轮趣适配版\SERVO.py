# 舵机控制 - 轮趣K210适配版
# 适配说明：串口引脚6/7改为0/1，波特率115200改为9600，PWM引脚8/9改为34/35

from machine import Timer,PWM
import time
from math import sqrt, pi
import math
import time
from machine import UART,Timer
from fpioa_manager import fm
condition=1
if condition:
    def func_servo(id0,posit0,interval0):
            ZT1=0xFF
            ZT2=0xFF
            DATA1=0X2A
            DATA2=(posit0>>8)&0xff
            DATA3=posit0&0xff
            DATA4=(interval0>>8)&0xff
            DATA5=interval0&0xff  # 条件满足时执行的代码
            data_length=0x07
            WriteDATA=0X03
            GetChecksum=(~(id0+data_length+WriteDATA+DATA1+DATA2+DATA3+DATA4+DATA5))& 0xff
            text=bytes([ZT1,ZT2,id0,data_length,WriteDATA,DATA1,DATA2,DATA3,<PERSON><PERSON>A<PERSON>,DATA<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>])
            uart.write(text) #数据回传

    intiposit=2048
    posit=2048
    interval=1000
    ID1=0x01
    ID2=0x02
    theta=0

    #映射串口引脚 - 轮趣K210适配
    fm.register(1, fm.fpioa.UART1_RX, force=True)  # 原引脚6改为引脚1
    fm.register(0, fm.fpioa.UART1_TX, force=True)  # 原引脚7改为引脚0

    #初始化串口 - 轮趣K210适配波特率
    uart = UART(UART.UART1, 9600, read_buf_len=4096)  # 115200改为9600
    uart.write('Hello 轮趣K210!')
    func_servo(ID1,int(posit),interval)
    func_servo(ID2,int(posit),interval)
    time.sleep(3)
    while True:

        #text=uart.read() #读取数据
        #print(text.decode('hex'))

        theta=theta+0.001
        if theta>=2*pi:
            theta=0

        r=pi*40
        x=r*math.cos(theta)
        y=r*math.sin(theta)
        func_servo(ID1,int(posit+x),interval)
        func_servo(ID2,int(posit+y),interval)
        print(x)
        print(y)
        #text=bytes([ZT1,ZT2,ID1,data_length,WriteDATA,DATA1,DATA2,DATA3,DATA4,DATA5,GetChecksum])
        #uart.write(text) #数据回传




else:
    def func_servo(servo,angle):
        servo.duty((angle+90)/180*10+2.5)

    tim = Timer(Timer.TIMER0, Timer.CHANNEL0, mode=Timer.MODE_PWM)
    S1 = PWM(tim, freq=50, duty=0, pin=34)  # 原引脚8改为引脚34
    tim2 = Timer(Timer.TIMER0, Timer.CHANNEL1, mode=Timer.MODE_PWM)
    S2 = PWM(tim2, freq=50, duty=0, pin=35)  # 原引脚9改为引脚35
    theta=0
    intheta=0
    func_servo(S1,0)
    func_servo(S2,0)
    time.sleep(3)
    while True:

        theta=theta+0.0001
        if theta>=2*pi:
            theta=0

        r=pi*3
        x=r*math.cos(theta)
        y=r*math.sin(theta)
        func_servo(S1,int(intheta+x))
        func_servo(S2,int(intheta+y))
        print(x)
        print(y)
