# K210云台控制和识别 - 轮趣K210适配版

## 项目说明
本项目是将原01Studio K210云台控制代码适配到轮趣K210开发板的版本。

## 硬件适配说明

### 引脚映射对比
| 功能 | 01Studio配置 | 轮趣K210配置 |
|------|-------------|-------------|
| 串口TX | 引脚7 | 引脚0 |
| 串口RX | 引脚6 | 引脚1 |
| 按键GPIO | 引脚16 | board_info.BOOT_KEY |
| I2C SCL | 引脚30 | 引脚32（自由选择） |
| I2C SDA | 引脚31 | 引脚33（自由选择） |
| PWM舵机 | 引脚8/9 | 不使用（串口控制） |

### 适配完成的功能
✅ **usart.py** - 串口通信功能
- 引脚映射：6/7 → 1/0
- 波特率：115200 → 9600
- 测试：串口数据收发正常

✅ **SERVO.py** - 舵机控制功能
- 串口引脚：6/7 → 1/0
- 波特率：115200 → 9600
- 控制方式：串口控制舵机（不使用PWM）
- 测试：双轴云台圆周运动

✅ **find_rect.py** - 矩形检测功能
- 摄像头配置：添加24MHz高频优化
- 测试：矩形检测和角点标记

✅ **threshold.py** - 触摸屏颜色阈值调试
- 按键引脚：16 → board_info.BOOT_KEY
- I2C引脚：自由选择32/33（轮趣K210可任意配置）
- 摄像头：添加24MHz配置
- 测试：触摸调节颜色阈值

### 配置变更
- 串口波特率：115200 → 9600
- 摄像头频率：默认 → 24MHz
- 按键配置：使用board_info抽象
- I2C引脚：自由配置（轮趣K210特性）

## 文件说明
- `threshold.py` - 触摸屏颜色阈值调试（适配版）
- `find_rect.py` - 矩形检测功能（适配版）
- `SERVO.py` - 舵机控制（适配版）
- `usart.py` - 串口通信（适配版）

## 使用说明
1. 将代码上传到轮趣K210开发板
2. 确保硬件连接正确
3. 运行对应的功能文件

## 注意事项
- 确保使用轮趣K210开发板
- 轮趣K210的IO引脚可自由配置，无预定义功能
- 串口波特率已调整为9600
- I2C引脚32/33可根据实际需要调整
- 舵机使用串口控制，不需要PWM引脚
