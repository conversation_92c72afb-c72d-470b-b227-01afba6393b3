'''
实验名称：触摸屏应用 - 轮趣K210适配版
版本： v1.0
日期： 2022.9
翻译和注释： 01Studio (轮趣K210适配)
适配说明：按键引脚16改为board_info.BOOT_KEY，I2C引脚30/31改为32/33，添加24MHz摄像头配置
'''

#导入相关模块
import sensor, image, time, lcd
from machine import I2C
from fpioa_manager import fm
from maix import GPIO
import lcd, image
import touchscreen as ts
import lcd,image,utime
from board import board_info  # 轮趣K210 board_info支持

lcd.init(freq=15000000)             #初始化LCD
sensor.reset()                      #复位和初始化摄像头，执行sensor.run(0)停止。
sensor.reset(freq=24000000, dual_buff=1)  # 轮趣K210 24MHz高频配置
sensor.set_vflip(1)                 #将摄像头设置成后置方式（所见即所得）
sensor.set_hmirror(1)               #GC0328摄像头（如果使用ov2640摄像头，注释此行。）

sensor.set_pixformat(sensor.RGB565) # 设置像素格式为彩色 RGB565 (或灰色)
sensor.set_framesize(sensor.QVGA)   # 设置帧大小为 QVGA (320x240)
sensor.skip_frames(time = 2000)     # 等待设置生效.
clock = time.clock()                # 创建一个时钟来追踪 FPS（每秒拍摄帧数）

#按键KEY用于清屏 - 轮趣K210适配
fm.register(board_info.BOOT_KEY, fm.fpioa.GPIOHS0, force=True)  # 使用board_info.BOOT_KEY
btn_clear = GPIO(GPIO.GPIOHS0, GPIO.IN, GPIO.PULL_NONE)  # 轮趣K210按键配置

#触摸使用I2C控制（NS2009） - 轮趣K210适配引脚
i2c = I2C(I2C.I2C0, freq=400000, scl=32, sda=33)  # 原引脚30/31改为32/33

#触摸屏初始化
ts.init(i2c)
#ts.calibrate() #触摸校准

#LCD初始化

lcd.clear()

#新建图像和触摸屏相关参数变量
lo=30
lh=100
ao=15
ah=127
bo=15
bh=127
lo2=0
lh2=100
ao2=-128
ah2=127
bo2=-128
bh2=127
status_last = ts.STATUS_IDLE
x_last = 0
y_last = 0
draw = False
thresholds = [(lo, lh, ao, ah, bo, bh)] # 蓝色阈值
low_threshold = []
while True:

    #获取触摸屏状态
    (status,x,y) = ts.read()

    img = sensor.snapshot()         # 拍摄一个图片并保存.
    print(status, x, y)
    #画图
     #画矩形：绿色不填充。



    ##更新最后坐标
    #x_last = x
    #y_last = y
    if status <3 :
        x=160
        y=120
    if x>5 and x<65  and  y>5 and y<35 :
        lo=lo-2
    if x>5 and x<65  and  y>45 and y<75 :
        lh=lh-2
    if x>5 and x<65  and  y>85 and y<115 :
        ao=ao-2
    if x>5 and x<65  and  y>125 and y<155 :
        ah=ah-2
    if x>5 and x<65  and  y>165 and y<195 :
        bo=bo-2
    if x>5 and x<65  and  y>205 and y<235 :
        bh=bh-2

    if x>255 and x<320  and  y>5 and y<35 :
        lo=lo+2
    if x>255 and x<320  and  y>45 and y<75 :
        lh=lh+2
    if x>255 and x<320  and  y>85 and y<115 :
        ao=ao+2
    if x>255 and x<320  and  y>125 and y<155 :
        ah=ah+2
    if x>255 and x<320  and  y>165 and y<195 :
        bo=bo+2
    if x>255 and x<320  and  y>205 and y<235 :
        bh=bh+2


    if lo<=1 :
        lo=1
    if bo<=-127 :
        bo=-127
    if ao<=-127 :
        ao=-127
    if lh<=1 :
        lh=1
    if bh<=-127 :
        bh=-127
    if ah<=-127 :
        ah=-127

    if lo>=99 :
        lo=99
    if bo>=127 :
        bo=127
    if ao>=127 :
        ao=127
    if lh>=100 :
        lh=100
    if bh>=127 :
        bh=127
    if ah>=127 :
        ah=127

    thresholds = [(lo, lh, ao, ah, bo, bh)] # 蓝色阈值
    img.binary(thresholds, invert=False, zero=False)
    if draw :
        img.draw_circle(x,y,20,color=(128,128,1),thickness = 2,fill=True)
    img.draw_rectangle(5, 5, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(5, 45, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(5, 85, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(5, 125, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(5, 165, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(5, 205, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)

    img.draw_rectangle(255, 5, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(255, 45, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(255, 85, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(255, 125, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(255, 165, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)
    img.draw_rectangle(255, 205, 60, 30, color = (0, 255, 0), thickness = 2, fill = False)

    img.draw_string(70, 20, str(lo), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 60, str(lh), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 100, str(ao), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 140, str(ah), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 180, str(bo), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 220, str(bh), color = (128, 1, 128), scale = 1,mono_space = False)

    img.draw_string(70, 20, str(lo), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 60, str(lh), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 100, str(ao), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 140, str(ah), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 180, str(bo), color = (128, 1, 128), scale = 1,mono_space = False)
    img.draw_string(70, 220, str(bh), color = (128, 1, 128), scale = 1,mono_space = False)
    #根据触摸屏状态判断是否继续执行画图功能
    if status_last!=status:
        if (status==ts.STATUS_PRESS or status == ts.STATUS_MOVE):
            draw = True
        else: #松开
            draw = False
        status_last = status

    #LCD显示


    lcd.display(img)

    #按键KEY按下清屏 - 轮趣K210适配
    if btn_clear.value() == 0:  # 轮趣K210按键检测
        img.clear()
